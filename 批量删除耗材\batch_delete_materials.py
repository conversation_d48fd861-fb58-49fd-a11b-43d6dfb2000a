# -*- coding: utf-8 -*-
import sys
import os
import pandas as pd
import requests
import json
import time
import logging
import datetime
import concurrent.futures
from tqdm import tqdm

# 添加父目录到系统路径，以便导入父目录中的模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入获取token的函数
from get_token import get_new_token

# 设置日志
def setup_logger():
    # 创建logs目录（如果不存在）
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建日志文件名，包含当前日期和时间
    current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"batch_delete_{current_time}.log")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    return log_file

def read_project_codes_from_excel(excel_file):
    """
    从Excel文件中读取项目编码列表
    
    Args:
        excel_file (str): Excel文件路径
        
    Returns:
        list: 项目编码列表
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 查找项目编码列
        code_column = None
        for col in df.columns:
            if '项目编码' in str(col):
                code_column = col
                break
        
        if code_column is None:
            logging.error("Excel文件中未找到'项目编码'列")
            return []
        
        # 提取有效的项目编码（跳过NaN和说明行）
        project_codes = []
        for code in df[code_column]:
            if pd.notna(code) and isinstance(code, (int, str)) and not str(code).startswith('说明'):
                # 确保编码是字符串格式
                project_codes.append(str(code).strip())
        
        return project_codes
    
    except Exception as e:
        logging.error(f"读取Excel文件时出错: {e}")
        return []

def get_project_info(keyword, token, session=None):
    """
    调用API接口获取产品服务信息
    
    Args:
        keyword (str): 查询关键字
        token (str): JWT令牌
        session (requests.Session, optional): 请求会话对象
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    logging.info(f"\n=== 查询项目信息 [{keyword}] ===")
    
    try:
        # 使用会话对象或创建新的请求
        if session:
            response = session.get(url, params=params, headers=headers, timeout=10)
        else:
            response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            logging.error(f"项目信息获取失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        logging.error(f"请求异常: {e}")
        return None

def get_project_and_materials(data):
    """
    从API响应数据中提取项目ID和耗材ID列表
    
    Args:
        data (dict): API返回的JSON数据
        
    Returns:
        tuple: (项目ID, 耗材ID列表, 项目名称)
    """
    if not data or "content" not in data or not data["content"]:
        logging.warning("未找到项目信息")
        return None, [], None
    
    project = data["content"][0]  # 获取第一个项目
    project_id = project.get("id")
    project_name = project.get("name")
    project_code = project.get("code")
    
    logging.info(f"项目ID: {project_id}")
    logging.info(f"项目名称: {project_name}")
    logging.info(f"项目编码: {project_code}")
    
    materials = project.get("materialDtos", [])
    material_ids = []
    
    if materials:
        logging.info(f"找到 {len(materials)} 个耗材")
        for material in materials:
            material_id = material.get("id")
            if material_id:
                material_ids.append(material_id)
    else:
        logging.info("该项目没有关联的耗材")
    
    return project_id, material_ids, project_name

def delete_materials(project_id, material_ids, token, session=None):
    """
    删除项目的耗材
    
    Args:
        project_id (str): 项目ID
        material_ids (list): 要删除的耗材ID列表
        token (str): 认证令牌
        session (requests.Session, optional): 请求会话对象
        
    Returns:
        bool: 删除操作是否成功
    """
    if not project_id or not material_ids:
        logging.error("缺少项目ID或耗材ID，无法执行删除操作")
        return False
    
    # 构建请求参数
    data = {
        "careServiceId": project_id,
        "careServiceMaterialIds": material_ids
    }
    
    # API URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/deleteMaterial"
    
    # 请求头
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    logging.info(f"开始删除 {len(material_ids)} 个耗材...")
    
    try:
        # 使用会话对象或创建新的请求
        if session:
            response = session.post(url, json=data, headers=headers, timeout=10)
        else:
            response = requests.post(url, json=data, headers=headers, timeout=10)
        
        if response.status_code == 200:
            logging.info("耗材删除成功!")
            return True
        else:
            logging.error(f"耗材删除失败: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        logging.error(f"请求异常: {e}")
        return False

def verify_deletion(keyword, token, project_name, session=None):
    """
    验证删除结果
    
    Args:
        keyword (str): 项目关键字
        token (str): JWT令牌
        project_name (str): 项目名称
        session (requests.Session, optional): 请求会话对象
    """
    # 等待0.5秒，确保删除操作已完成
    time.sleep(0.5)
    
    # 重新获取项目信息
    updated_data = get_project_info(keyword, token, session)
    
    if not updated_data:
        logging.warning("无法获取最新项目信息，无法验证删除结果")
        return
    
    # 检查剩余耗材
    project_id, remaining_ids, _ = get_project_and_materials(updated_data)
    
    if not remaining_ids:
        logging.info(f"确认: '{project_name}' 的所有耗材已成功删除!")
    else:
        logging.warning(f"警告: '{project_name}' 仍有 {len(remaining_ids)} 个耗材未删除")

def process_project(project_code, token):
    """
    处理单个项目的耗材删除
    
    Args:
        project_code (str): 项目编码
        token (str): JWT令牌
        
    Returns:
        bool: 处理是否成功
    """
    # 创建会话对象，重用连接提高效率
    session = requests.Session()
    
    # 获取项目信息
    data = get_project_info(project_code, token, session)
    if not data:
        logging.error(f"项目 {project_code} 信息获取失败，跳过处理")
        return False
    
    # 获取项目ID和耗材列表
    project_id, material_ids, project_name = get_project_and_materials(data)
    
    if not project_id:
        logging.error(f"项目 {project_code} 未找到有效的项目ID，跳过处理")
        return False
    
    if not material_ids:
        logging.info(f"项目 {project_code} 没有关联的耗材，无需删除")
        return True
    
    # 执行删除操作
    success = delete_materials(project_id, material_ids, token, session)
    
    if success:
        # 验证删除结果
        verify_deletion(project_code, token, project_name, session)
        return True
    else:
        logging.error(f"项目 {project_code} 耗材删除失败")
        return False

def process_projects_parallel(project_codes, token, max_workers=5):
    """
    并行处理多个项目的耗材删除
    
    Args:
        project_codes (list): 项目编码列表
        token (str): JWT令牌
        max_workers (int): 最大并发数
        
    Returns:
        tuple: (成功数量, 失败数量)
    """
    success_count = 0
    fail_count = 0
    
    # 使用线程池并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_code = {executor.submit(process_project, code, token): code for code in project_codes}
        
        # 使用tqdm创建进度条
        with tqdm(total=len(project_codes), desc="处理进度") as pbar:
            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_code):
                code = future_to_code[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    logging.error(f"处理项目 {code} 时发生异常: {e}")
                    fail_count += 1
                
                # 更新进度条
                pbar.update(1)
    
    return success_count, fail_count

def main():
    # 设置日志
    log_file = setup_logger()
    
    # 默认Excel文件路径
    excel_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "项目耗材删除模板.xlsx")
    
    # 检查命令行参数，允许指定其他Excel文件
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
    
    if not os.path.exists(excel_file):
        logging.error(f"错误：Excel文件 '{excel_file}' 不存在")
        return
    
    # 读取项目编码
    logging.info(f"=== 从Excel文件 '{excel_file}' 读取项目编码 ===")
    project_codes = read_project_codes_from_excel(excel_file)
    
    if not project_codes:
        logging.error("未找到有效的项目编码，请检查Excel文件格式")
        return
    
    logging.info(f"找到 {len(project_codes)} 个项目编码")
    
    # 获取token
    logging.info("\n=== 获取认证Token ===")
    token = get_new_token()
    
    if not token:
        logging.error("获取Token失败，无法继续")
        return
    
    # 并行处理项目
    logging.info("\n=== 开始批量处理项目 ===")
    
    # 设置并发数（可根据需要调整）
    max_workers = 10
    logging.info(f"并发处理数: {max_workers}")
    
    start_time = time.time()
    success_count, fail_count = process_projects_parallel(project_codes, token, max_workers)
    end_time = time.time()
    
    # 打印处理结果统计
    logging.info("\n=== 批量处理完成 ===")
    logging.info(f"总项目数: {len(project_codes)}")
    logging.info(f"成功处理: {success_count}")
    logging.info(f"处理失败: {fail_count}")
    logging.info(f"处理耗时: {end_time - start_time:.2f} 秒")
    logging.info(f"日志文件已保存到: {log_file}")

if __name__ == "__main__":
    main() 