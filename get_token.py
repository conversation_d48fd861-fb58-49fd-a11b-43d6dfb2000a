import requests
import json
import sys

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    print(f"请求参数: appId={data['appId']}, code={data['code']}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    
                    # 保存token到文件
                    with open("token.txt", "w") as f:
                        f.write(token)
                    print("Token已保存到token.txt文件")
                    
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    
                    # 保存token到文件
                    with open("token.txt", "w") as f:
                        f.write(token)
                    print("Token已保存到token.txt文件")
                    
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def main():
    # 获取新token
    token = get_new_token()
    
    if token:
        print("\n=== 新Token获取成功 ===")
        print("您可以使用以下命令删除项目耗材:")
        print(f"python delete_materials.py 250703002651 \"{token}\"")
        print("或者自动删除所有耗材:")
        print(f"python auto_delete_materials.py 250703002651 \"{token}\"")
    else:
        print("\n=== 获取Token失败 ===")
        print("请检查网络连接或API参数是否正确")

if __name__ == "__main__":
    main() 