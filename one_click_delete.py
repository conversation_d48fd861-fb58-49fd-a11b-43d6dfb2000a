import requests
import json
import sys
import time

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_project_info(keyword, token):
    """
    调用API接口获取产品服务信息
    
    Args:
        keyword (str): 查询关键字
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print("\n=== 查询项目信息 ===")
    print(f"请求URL: {url}")
    print(f"查询关键字: {keyword}")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            print("项目信息获取成功!")
            return result
        else:
            print(f"项目信息获取失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_project_and_materials(data):
    """
    从API响应数据中提取项目ID和耗材ID列表
    
    Args:
        data (dict): API返回的JSON数据
        
    Returns:
        tuple: (项目ID, 耗材ID列表, 项目名称)
    """
    if not data or "content" not in data or not data["content"]:
        print("未找到项目信息")
        return None, [], None
    
    project = data["content"][0]  # 获取第一个项目
    project_id = project.get("id")
    project_name = project.get("name")
    project_code = project.get("code")
    
    print(f"\n=== 项目信息 ===")
    print(f"项目ID: {project_id}")
    print(f"项目名称: {project_name}")
    print(f"项目编码: {project_code}")
    
    materials = project.get("materialDtos", [])
    material_ids = []
    
    if materials:
        print(f"\n找到 {len(materials)} 个耗材")
        for idx, material in enumerate(materials, 1):
            material_id = material.get("id")
            if material_id:
                material_ids.append(material_id)
                print(f"{idx}. 耗材: {material.get('materialName', '未知')} (ID: {material_id})")
    else:
        print("该项目没有关联的耗材")
    
    return project_id, material_ids, project_name

def delete_materials(project_id, material_ids, token):
    """
    删除项目的耗材
    
    Args:
        project_id (str): 项目ID
        material_ids (list): 要删除的耗材ID列表
        token (str): 认证令牌
        
    Returns:
        bool: 删除操作是否成功
    """
    if not project_id or not material_ids:
        print("缺少项目ID或耗材ID，无法执行删除操作")
        return False
    
    # 构建请求参数
    data = {
        "careServiceId": project_id,
        "careServiceMaterialIds": material_ids
    }
    
    # API URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/deleteMaterial"
    
    # 请求头
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print("\n=== 开始删除耗材 ===")
    print(f"请求URL: {url}")
    print(f"项目ID: {project_id}")
    print(f"待删除耗材数量: {len(material_ids)}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码和内容
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("耗材删除成功!")
            try:
                result = response.json()
                print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            except:
                print(f"响应内容: {response.text}")
            return True
        else:
            print(f"耗材删除失败: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return False

def verify_deletion(keyword, token, project_name):
    """
    验证删除结果
    
    Args:
        keyword (str): 项目关键字
        token (str): JWT令牌
        project_name (str): 项目名称
    """
    print("\n=== 验证删除结果 ===")
    print("正在重新获取项目信息...")
    
    # 等待1秒，确保删除操作已完成
    time.sleep(1)
    
    # 重新获取项目信息
    updated_data = get_project_info(keyword, token)
    
    if not updated_data:
        print("无法获取最新项目信息，无法验证删除结果")
        return
    
    # 检查剩余耗材
    project_id, remaining_ids, _ = get_project_and_materials(updated_data)
    
    if not remaining_ids:
        print(f"\n确认: '{project_name}' 的所有耗材已成功删除!")
    else:
        print(f"\n警告: '{project_name}' 仍有 {len(remaining_ids)} 个耗材未删除")
        print("您可以再次运行此脚本删除剩余耗材")

def main():
    # 获取项目编码
    if len(sys.argv) > 1:
        keyword = sys.argv[1]
    else:
        print("请输入要查询的项目编码:")
        keyword = input("> ").strip()
        
        if not keyword:
            print("未提供项目编码，退出程序")
            return
    
    print(f"将为项目 '{keyword}' 执行耗材删除操作")
    
    # 获取新token
    token = get_new_token()
    
    if not token:
        print("Token获取失败，无法继续操作")
        return
        
    # 查询项目信息
    project_data = get_project_info(keyword, token)
    
    if not project_data:
        print("项目信息获取失败，无法继续操作")
        return
    
    # 获取项目ID和耗材ID列表
    project_id, material_ids, project_name = get_project_and_materials(project_data)
    
    if not project_id:
        print("未找到有效的项目ID，操作终止")
        return
    
    if not material_ids:
        print("该项目没有关联的耗材，无需删除")
        return
    
    # 确认删除
    print(f"\n准备删除 '{project_name}' 的所有 {len(material_ids)} 个耗材")
    print("是否继续? (y/n)")
    confirm = input("> ").strip().lower()
    
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 执行删除操作
    success = delete_materials(project_id, material_ids, token)
    
    if success:
        print("\n删除操作执行完成")
        # 验证删除结果
        verify_deletion(keyword, token, project_name)
    else:
        print("\n删除操作失败")

if __name__ == "__main__":
    main() 