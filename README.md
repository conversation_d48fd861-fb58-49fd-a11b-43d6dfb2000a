# 删除项目的所属耗材

这个Python工具用于删除指定项目的耗材。它首先通过项目编码查询项目信息，然后允许用户选择要删除的耗材。

## 功能说明

1. 通过项目编码查询项目信息
2. 显示项目的所有耗材列表
3. 允许用户选择要删除的特定耗材
4. 执行删除操作
5. 验证删除结果

## 可用脚本

本工具提供以下脚本：

1. `get_token.py` - 获取新的API访问令牌
2. `delete_materials.py` - 交互式删除耗材脚本
3. `auto_delete_materials.py` - 自动删除所有耗材的脚本
4. `one_click_delete.py` - 一键式操作，自动获取令牌并删除耗材

## 使用前准备

安装必要的依赖：

```bash
pip install requests
```

## 使用方法

本工具提供两个脚本：交互式脚本和自动化脚本。

### 交互式脚本 (delete_materials.py)

#### 方法1：直接运行脚本

```bash
python delete_materials.py
```

程序会提示您输入项目编码。

#### 方法2：通过命令行参数指定项目编码

```bash
python delete_materials.py 250703002651
```

其中`250703002651`为要查询的项目编码。

#### 方法3：指定项目编码和Token

```bash
python delete_materials.py 250703002651 "your-jwt-token"
```

当默认Token过期时，可以通过此方式提供新的Token。

### 自动化脚本 (auto_delete_materials.py)

自动删除指定项目的所有耗材，无需用户交互：

```bash
python auto_delete_materials.py 250703002651
```

使用自定义Token：

```bash
python auto_delete_materials.py 250703002651 "your-jwt-token"
```

自动化脚本必须提供项目编码作为命令行参数，Token参数是可选的。

### 获取新Token (get_token.py)

当Token过期时，可以使用此脚本获取新的Token：

```bash
python get_token.py
```

### 一键式操作 (one_click_delete.py)

此脚本自动执行所有步骤，包括获取新Token和删除耗材：

```bash
python one_click_delete.py 250703002651
```

或者直接运行，然后输入项目编码：

```bash
python one_click_delete.py
```

## 交互流程

1. 输入或指定项目编码
2. 程序查询并显示项目信息及耗材列表
3. 选择要删除的耗材：
   - 输入编号（如`1,2,3`）删除特定耗材
   - 输入`all`删除所有耗材
   - 输入`0`取消操作
4. 确认删除操作
5. 程序执行删除并显示结果

## API说明

本工具使用了以下API：

1. 查询项目信息：`https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service`
2. 删除项目耗材：`https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/deleteMaterial`

认证方式：通过`x-access-token`请求头传递JWT token 