import requests
import json
import sys
import os

def get_project_info(keyword="250703002651", token=None):
    """
    调用API接口获取产品服务信息
    
    Args:
        keyword (str): 查询关键字，默认为"250703002651"
        token (str): JWT令牌，如果未提供则使用默认令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # 如果未提供token，则使用默认token
    if token is None:
        token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJsYyIsImNvZCI6InNoLXpteWgiLCJleHAiOjE3NTE1Mjk0MTcsImlhdCI6MTc1MTUyNzYxNywiYWlkIjoiNDBlMzhhNTgtNWM2Ni00MTIwLTk5N2EtYmVmZDU5NWUwZTQ4In0.oeO6dMSthg-DN7m9jQPYbRH_9ZidGWfBR5kWuD-UnWI"
    
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print("=== 查询项目信息 ===")
    print(f"请求URL: {url}")
    print(f"查询关键字: {keyword}")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            print("项目信息获取成功!")
            return result, token
        else:
            print(f"项目信息获取失败: {response.text}")
            return None, token
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None, token

def display_materials(data):
    """
    显示项目的所有耗材信息，并返回项目ID和耗材ID列表
    
    Args:
        data (dict): API返回的JSON数据
        
    Returns:
        tuple: (项目ID, 耗材ID列表)
    """
    if not data or "content" not in data or not data["content"]:
        print("未找到项目信息")
        return None, []
    
    project = data["content"][0]  # 获取第一个项目
    project_id = project.get("id")
    project_name = project.get("name")
    project_code = project.get("code")
    
    print(f"\n=== 项目信息 ===")
    print(f"项目ID: {project_id}")
    print(f"项目名称: {project_name}")
    print(f"项目编码: {project_code}")
    
    materials = project.get("materialDtos", [])
    material_ids = []
    
    if materials:
        print("\n=== 耗材列表 ===")
        for idx, material in enumerate(materials, 1):
            material_id = material.get("id")
            material_ids.append(material_id)
            print(f"{idx}. ID: {material_id}")
            print(f"   名称: {material.get('materialName', '未知')}")
            print(f"   规格: {material.get('spec', '未知')}")
            print(f"   单位: {material.get('unit', '未知')}")
            print()
    else:
        print("该项目没有关联的耗材")
    
    return project_id, material_ids

def delete_materials(project_id, material_ids, token):
    """
    删除项目的耗材
    
    Args:
        project_id (str): 项目ID
        material_ids (list): 要删除的耗材ID列表
        token (str): 认证令牌
        
    Returns:
        bool: 删除操作是否成功
    """
    if not project_id or not material_ids:
        print("缺少项目ID或耗材ID，无法执行删除操作")
        return False
    
    # 构建请求参数
    data = {
        "careServiceId": project_id,
        "careServiceMaterialIds": material_ids
    }
    
    # API URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/deleteMaterial"
    
    # 请求头
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print("\n=== 开始删除耗材 ===")
    print(f"请求URL: {url}")
    print(f"项目ID: {project_id}")
    print(f"待删除耗材数量: {len(material_ids)}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码和内容
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("耗材删除成功!")
            try:
                result = response.json()
                print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            except:
                print(f"响应内容: {response.text}")
            return True
        else:
            print(f"耗材删除失败: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return False

def select_materials_to_delete(material_ids):
    """
    让用户选择要删除的耗材
    
    Args:
        material_ids (list): 所有耗材ID列表
        
    Returns:
        list: 用户选择要删除的耗材ID列表
    """
    if not material_ids:
        return []
    
    print("\n=== 选择要删除的耗材 ===")
    print("请输入要删除的耗材编号(多个编号用逗号分隔，输入'all'删除所有，输入'0'取消):")
    
    choice = input("> ").strip()
    
    if choice.lower() == 'all':
        return material_ids
    elif choice == '0':
        return []
    else:
        try:
            # 解析用户输入的编号
            indices = [int(idx.strip()) for idx in choice.split(',') if idx.strip()]
            selected_ids = []
            
            for idx in indices:
                if 1 <= idx <= len(material_ids):
                    selected_ids.append(material_ids[idx-1])
            
            return selected_ids
        except:
            print("输入格式错误，取消删除操作")
            return []

def main():
    # 处理命令行参数
    keyword = None
    token = None
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        keyword = sys.argv[1]
    
    if len(sys.argv) > 2:
        token = sys.argv[2]
        print(f"使用提供的token: {token[:10]}...")
    
    # 如果未通过命令行提供关键字，则提示用户输入
    if not keyword:
        print("请输入要查询的项目关键字(编码):")
        keyword = input("> ").strip()
    
    # 调用API获取项目信息
    data, token = get_project_info(keyword, token)
    
    if not data:
        print("无法继续执行删除操作")
        return
    
    # 显示项目信息和耗材列表
    project_id, material_ids = display_materials(data)
    
    if not project_id or not material_ids:
        print("该项目没有可删除的耗材")
        return
    
    # 用户选择要删除的耗材
    selected_ids = select_materials_to_delete(material_ids)
    
    if not selected_ids:
        print("未选择任何耗材，操作取消")
        return
    
    # 确认删除
    print(f"\n将删除 {len(selected_ids)}/{len(material_ids)} 个耗材，确认操作? (y/n)")
    confirm = input("> ").strip().lower()
    
    if confirm == 'y':
        # 执行删除操作
        success = delete_materials(project_id, selected_ids, token)
        
        if success:
            print("\n删除操作已完成")
            
            # 再次查询项目信息，确认删除结果
            print("\n正在重新获取项目信息...")
            updated_data, _ = get_project_info(keyword, token)
            
            if updated_data:
                _, remaining_ids = display_materials(updated_data)
                print(f"剩余耗材数量: {len(remaining_ids)}")
        else:
            print("\n删除操作失败")
    else:
        print("操作已取消")

if __name__ == "__main__":
    main() 